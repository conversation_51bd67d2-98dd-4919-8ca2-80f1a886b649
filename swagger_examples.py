#!/usr/bin/env python3
"""
Swagger/OpenAPI Examples for the Common Class API
This file contains detailed examples and schemas for API documentation.
"""

# LINE Send to All Friends - Comprehensive Examples
LINE_SEND_TO_ALL_FRIENDS_EXAMPLES = {
    "text_only": {
        "summary": "Send Text Message Only",
        "description": "Send a simple text message to all friends of the LINE token",
        "value": {
            "token": "your_line_channel_access_token_here",
            "message": "🎉 Hello everyone! This is a broadcast message from our service."
        }
    },
    "text_with_image": {
        "summary": "Send Text + Image",
        "description": "Send a text message along with an image to all friends",
        "value": {
            "token": "your_line_channel_access_token_here",
            "message": "📸 Check out this amazing image!",
            "img_url": "https://picsum.photos/800/600"
        }
    },
    "text_with_video": {
        "summary": "Send Text + Video",
        "description": "Send a text message along with a video to all friends",
        "value": {
            "token": "your_line_channel_access_token_here",
            "message": "🎬 Watch this awesome video!",
            "vd_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
        }
    },
    "all_content_types": {
        "summary": "Send Text + Image + Video",
        "description": "Send all content types together - text, image, and video",
        "value": {
            "token": "your_line_channel_access_token_here",
            "message": "🚀 Complete content package! Text, image, and video all together.",
            "img_url": "https://picsum.photos/800/600",
            "vd_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
        }
    },
    "image_only": {
        "summary": "Send Image Only",
        "description": "Send only an image without text message",
        "value": {
            "token": "your_line_channel_access_token_here",
            "img_url": "https://picsum.photos/800/600"
        }
    },
    "video_only": {
        "summary": "Send Video Only",
        "description": "Send only a video without text message",
        "value": {
            "token": "your_line_channel_access_token_here",
            "vd_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
        }
    }
}

# Response Examples
LINE_SEND_TO_ALL_FRIENDS_RESPONSES = {
    "success_text_only": {
        "summary": "Successful Text Message Response",
        "value": {
            "status": "success",
            "message": "Successfully sent text to all friends",
            "content_types": ["text"],
            "text_message": "Hello everyone!",
            "image_url": None,
            "video_url": None,
            "method": "send_to_all_friends",
            "message_count": 1,
            "messages_sent": [
                {
                    "type": "text",
                    "text": "Hello everyone!"
                }
            ],
            "endpoint": "message/broadcast",
            "api_call": "LINE Messaging API"
        }
    },
    "success_all_types": {
        "summary": "Successful All Content Types Response",
        "value": {
            "status": "success",
            "message": "Successfully sent text, image, video to all friends",
            "content_types": ["text", "image", "video"],
            "text_message": "Complete content package!",
            "image_url": "https://picsum.photos/800/600",
            "video_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
            "method": "send_to_all_friends",
            "message_count": 3,
            "messages_sent": [
                {
                    "type": "text",
                    "text": "Complete content package!"
                },
                {
                    "type": "image",
                    "originalContentUrl": "https://picsum.photos/800/600",
                    "previewImageUrl": "https://picsum.photos/800/600"
                },
                {
                    "type": "video",
                    "originalContentUrl": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
                    "previewImageUrl": "https://picsum.photos/800/600"
                }
            ],
            "endpoint": "message/broadcast",
            "api_call": "LINE Messaging API"
        }
    },
    "error_no_token": {
        "summary": "Error - Missing Token",
        "value": {
            "status": "error",
            "error": "LINE token is required"
        }
    },
    "error_no_content": {
        "summary": "Error - No Content Provided",
        "value": {
            "status": "error",
            "error": "At least one of message, img_url, or vd_url must be provided"
        }
    },
    "error_invalid_token": {
        "summary": "Error - Invalid LINE Token",
        "value": {
            "status": "error",
            "error": "Invalid channel access token",
            "status_code": 401
        }
    }
}

# Test Method Examples
LINE_TEST_SEND_TO_ALL_FRIENDS_EXAMPLES = {
    "test_all_types": {
        "summary": "Test All Content Types (No Real API Call)",
        "description": "Test the method with all content types without making real LINE API calls",
        "value": {
            "token": "test_token_123",
            "message": "This is a test message",
            "img_url": "https://example.com/test-image.jpg",
            "vd_url": "https://example.com/test-video.mp4"
        }
    },
    "test_text_only": {
        "summary": "Test Text Only (No Real API Call)",
        "description": "Test with only text message",
        "value": {
            "token": "test_token_123",
            "message": "Just testing text message"
        }
    }
}

# API Documentation Schema
API_DOCUMENTATION = {
    "line.send_to_all_friends": {
        "method": "POST",
        "endpoint": "/line.send_to_all_friends",
        "summary": "Send text, image, and/or video messages to all friends",
        "description": """
        Send multiple content types (text, image, video) to all friends of a LINE token in a single API call.
        
        This method combines the functionality of sending text messages, images, and videos through LINE's broadcast API.
        At least one content type must be provided.
        
        **Features:**
        - Send to all friends of the LINE token
        - Support for text, image, and video content
        - Automatic message object creation
        - Comprehensive error handling
        - Detailed response with sent content information
        
        **LINE API Limits:**
        - Maximum 5 messages per broadcast
        - Images: HTTPS URLs, JPEG/PNG, max 10MB original, max 1MB preview
        - Videos: HTTPS URLs, MP4, max 200MB, requires preview image
        - Text: Maximum 5000 characters
        """,
        "parameters": {
            "token": {
                "type": "string",
                "required": True,
                "description": "LINE Channel Access Token (required)"
            },
            "message": {
                "type": "string",
                "required": False,
                "description": "Text message to send (optional, max 5000 characters)"
            },
            "img_url": {
                "type": "string",
                "required": False,
                "description": "Image URL to send (optional, HTTPS, JPEG/PNG, max 10MB)"
            },
            "vd_url": {
                "type": "string",
                "required": False,
                "description": "Video URL to send (optional, HTTPS, MP4, max 200MB)"
            }
        },
        "examples": LINE_SEND_TO_ALL_FRIENDS_EXAMPLES,
        "responses": LINE_SEND_TO_ALL_FRIENDS_RESPONSES
    },
    "line.test_send_to_all_friends": {
        "method": "POST",
        "endpoint": "/line.test_send_to_all_friends",
        "summary": "Test send_to_all_friends method without real API calls",
        "description": """
        Test version of send_to_all_friends that simulates the functionality without making actual LINE API calls.
        Perfect for development and testing purposes.
        """,
        "parameters": {
            "token": {
                "type": "string",
                "required": False,
                "description": "Test token (any string)",
                "default": "test_token"
            },
            "message": {
                "type": "string",
                "required": False,
                "description": "Test message text",
                "default": "Test message"
            },
            "img_url": {
                "type": "string",
                "required": False,
                "description": "Test image URL"
            },
            "vd_url": {
                "type": "string",
                "required": False,
                "description": "Test video URL"
            }
        },
        "examples": LINE_TEST_SEND_TO_ALL_FRIENDS_EXAMPLES
    }
}

if __name__ == "__main__":
    import json
    print("LINE Send to All Friends - API Documentation")
    print("=" * 50)
    print(json.dumps(API_DOCUMENTATION, indent=2))
