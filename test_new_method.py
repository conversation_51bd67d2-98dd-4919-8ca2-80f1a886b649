#!/usr/bin/env python3
"""
Test script for the new send_to_all_friends method
"""

import requests
import json

# Configuration
API_BASE_URL = "http://127.0.0.1:8000"
API_TOKEN = "8a2c1cf846ba5e1245bfb3cc31a6d60a779ca49f02b65ddce1a355e91d01f8bd"

def call_api(endpoint: str, data: dict = None):
    """Helper function to call API methods."""
    url = f"{API_BASE_URL}/{endpoint}"
    headers = {
        'Authorization': f'Bearer {API_TOKEN}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.post(url, headers=headers, json=data or {})
        return {
            "status_code": response.status_code,
            "response": response.json() if response.text else {}
        }
    except Exception as e:
        return {"error": str(e)}

def test_new_method():
    """Test the new send_to_all_friends method."""
    print("🧪 Testing the new send_to_all_friends method...")
    
    # Test 1: Test method (no real API call)
    print("\n1. Testing with test method (no real API call):")
    result = call_api("line.test_send_to_all_friends", {
        "token": "test_token_123",
        "message": "Hello everyone! This is a test message.",
        "img_url": "https://picsum.photos/800/600",
        "vd_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
    })
    print(f"Status: {result.get('status_code')}")
    print(f"Response: {json.dumps(result.get('response', {}), indent=2)}")
    
    # Test 2: Text only
    print("\n2. Testing text only:")
    result = call_api("line.test_send_to_all_friends", {
        "token": "test_token_123",
        "message": "Just a text message"
    })
    print(f"Status: {result.get('status_code')}")
    print(f"Response: {json.dumps(result.get('response', {}), indent=2)}")
    
    # Test 3: Image only
    print("\n3. Testing image only:")
    result = call_api("line.test_send_to_all_friends", {
        "token": "test_token_123",
        "img_url": "https://picsum.photos/800/600"
    })
    print(f"Status: {result.get('status_code')}")
    print(f"Response: {json.dumps(result.get('response', {}), indent=2)}")
    
    # Test 4: Error case - no content
    print("\n4. Testing error case (no content):")
    result = call_api("line.test_send_to_all_friends", {
        "token": "test_token_123"
    })
    print(f"Status: {result.get('status_code')}")
    print(f"Response: {json.dumps(result.get('response', {}), indent=2)}")

def test_real_api():
    """Test with real LINE API (requires valid token)."""
    print("\n🚀 Testing with REAL LINE API...")
    print("⚠️  This will make actual API calls to LINE!")
    
    # You need to provide a real LINE token here
    real_token = input("Enter your real LINE token (or press Enter to skip): ").strip()
    
    if not real_token:
        print("Skipping real API test.")
        return
    
    # Test with real API
    print("\nSending real message to all friends...")
    result = call_api("line.send_to_all_friends", {
        "token": real_token,
        "message": "🎉 Hello from the new send_to_all_friends method! This is a test message.",
        "img_url": "https://picsum.photos/800/600"
    })
    
    print(f"Status: {result.get('status_code')}")
    print(f"Response: {json.dumps(result.get('response', {}), indent=2)}")

if __name__ == "__main__":
    test_new_method()
    
    # Ask if user wants to test with real API
    test_real = input("\nDo you want to test with real LINE API? (y/n): ").strip().lower()
    if test_real == 'y':
        test_real_api()
    
    print("\n✅ Testing completed!")
