#!/usr/bin/env python3
"""
Interactive test script for the new send_to_all_friends method with real LINE API
"""

import requests
import json

# Configuration
API_BASE_URL = "http://127.0.0.1:8000"
API_TOKEN = "8a2c1cf846ba5e1245bfb3cc31a6d60a779ca49f02b65ddce1a355e91d01f8bd"

def call_api(endpoint: str, data: dict = None, timeout: int = 30):
    """Helper function to call API methods."""
    url = f"{API_BASE_URL}/{endpoint}"
    headers = {
        'Authorization': f'Bearer {API_TOKEN}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.post(url, headers=headers, json=data or {}, timeout=timeout)
        return {
            "status_code": response.status_code,
            "response": response.json() if response.text else {}
        }
    except requests.exceptions.Timeout:
        return {"error": f"Request timed out after {timeout} seconds"}
    except Exception as e:
        return {"error": str(e)}

def main():
    print("🧪 Testing the new send_to_all_friends method with REAL LINE API")
    print("=" * 60)
    
    # Get LINE token from user
    line_token = input("Enter your LINE Channel Access Token: ").strip()
    
    if not line_token:
        print("❌ No token provided. Exiting.")
        return
    
    print(f"\n✅ Token received: {line_token[:20]}...")
    
    # Test 1: Text only
    print("\n1️⃣ Testing with text message only:")
    result = call_api("line.send_to_all_friends", {
        "token": line_token,
        "message": "🎉 Hello from the new send_to_all_friends method! This is a test message."
    })
    
    print(f"Status: {result.get('status_code')}")
    print(f"Response: {json.dumps(result.get('response', {}), indent=2)}")
    
    if result.get('status_code') != 200:
        print("❌ Text message test failed. Check your token and try again.")
        return
    
    # Ask if user wants to continue with more tests
    continue_test = input("\n✅ Text message test successful! Continue with image test? (y/n): ").strip().lower()
    
    if continue_test != 'y':
        print("Test completed.")
        return
    
    # Test 2: Text + Image
    print("\n2️⃣ Testing with text + image:")
    result = call_api("line.send_to_all_friends", {
        "token": line_token,
        "message": "📸 Here's a test image from the new method!",
        "img_url": "https://picsum.photos/800/600"
    })
    
    print(f"Status: {result.get('status_code')}")
    print(f"Response: {json.dumps(result.get('response', {}), indent=2)}")
    
    # Ask if user wants to test with video
    continue_test = input("\n✅ Image test completed! Test with video? (y/n): ").strip().lower()
    
    if continue_test != 'y':
        print("Test completed.")
        return
    
    # Test 3: Text + Image + Video
    print("\n3️⃣ Testing with text + image + video:")
    result = call_api("line.send_to_all_friends", {
        "token": line_token,
        "message": "🎬 Testing all content types together!",
        "img_url": "https://picsum.photos/800/600",
        "vd_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
    })
    
    print(f"Status: {result.get('status_code')}")
    print(f"Response: {json.dumps(result.get('response', {}), indent=2)}")
    
    print("\n🎉 All tests completed!")
    print("\n📋 Summary:")
    print("✅ The send_to_all_friends method is working correctly!")
    print("✅ It can send text, image, and video messages to all friends")
    print("✅ The method properly validates inputs and handles errors")

if __name__ == "__main__":
    main()
